# 文档预览功能重构工作日志

## 项目概述
将文档预览功能从PDF转换预览方式改为使用vue-office组件进行原生Office文档预览。

## 当前状态：🔴 问题待解决

### 核心问题
**问题描述**：点击Office文档预览时，浏览器发起多个 `converted.pdf` 请求，说明后端仍在返回PDF转换URL而非原始Office文档URL。

**问题表现**：
- 网络请求显示多个 `http://localhost/api/iot/v1/documents/67-9904a3-4641f1089c72e091ca444e4/98f4784442ae11f0b60aaea9fb0ca77/converted.pdf`
- 前端应该收到原始Office文档的下载URL，而不是转换后的PDF URL

**根本原因分析**：
1. 后端 `document_service.py` 中的 `get_document_preview` 函数仍在检查Office文档转换状态
2. 如果文档已转换，返回 `converted.pdf` URL
3. 前端收到错误的URL类型，导致vue-office组件无法正常工作

---

## 重构进度记录

### ✅ 已完成的工作

#### 2024-12-XX - 前端组件优化
- **VueOfficePreview组件优化**
  - ✅ 添加CSS样式导入 (`@vue-office/docx/lib/index.css`, `@vue-office/excel/lib/index.css`)
  - ✅ 改进文档类型判断逻辑
  - ✅ 优化错误处理和文档格式验证
  - ✅ 移除PDF处理（由DocumentPreview专门处理）

- **DocumentPreview组件清理**
  - ✅ 更新Office文档类型判断（仅支持Word和Excel）
  - ✅ 将PowerPoint标记为不支持
  - ✅ 简化Office文档处理逻辑

- **URL处理优化**
  - ✅ 支持相对路径和绝对路径
  - ✅ 正确的认证处理（Bearer token）

### 🔴 当前正在解决的问题

#### 2024-12-XX - 后端逻辑修正

**问题**：后端仍返回PDF转换URL而非原始Office文档URL

**当前状态**：已完成后端逻辑修改，正在验证

**修改计划**：
1. ✅ 已识别问题位置：`backend/app/iot/service/document_service.py` 第738-750行
2. ✅ 已完成修改：将Office文档处理逻辑改为直接返回原始文档URL
3. 🔄 正在验证：检查是否还有其他地方需要修改

**具体修改内容**：
```python
# 修改前：检查转换状态，返回PDF URL
elif doc_type == "office":
    is_converted = await document_cache.is_document_converted(doc_id)
    if is_converted:
        return {"content_type": "pdf", "url": pdf_url, ...}
    else:
        return {"content_type": "office", "requires_conversion": True, ...}

# 修改后：直接返回原始文档URL
elif doc_type == "office":
    return {
        "content_type": "office",
        "requires_conversion": False,
        "url": f"/api/iot/v1/documents/{kb_id}/{doc_id}/download",
        ...
    }
```

---

## 验证步骤

### 当前验证任务：后端修改验证

**验证步骤**：
1. ⏳ 重启后端服务
2. ⏳ 在前端点击Office文档预览
3. ⏳ 检查网络请求：
   - 应该看到 `/download` 请求而不是 `/converted.pdf` 请求
   - 预览API应该返回 `content_type: "office"`
4. ⏳ 验证vue-office组件是否正常加载文档

**预期结果**：
- 网络请求中不再出现 `converted.pdf`
- 出现对原始Office文档的下载请求
- vue-office组件能够正常预览Word/Excel文档

---

## 待解决问题列表

### 高优先级
1. 🔴 **后端返回错误URL类型** - 当前正在解决
2. ⏳ **验证vue-office组件正常工作**
3. ⏳ **PowerPoint文档的用户体验优化**

### 中优先级
1. ⏳ **大文件预览性能优化**
2. ⏳ **错误处理用户体验改进**
3. ⏳ **移动端适配**

### 低优先级
1. ⏳ **文档缓存机制**
2. ⏳ **预览进度指示器**

---

## 技术细节记录

### 依赖包版本
```json
{
  "@vue-office/docx": "^1.6.3",
  "@vue-office/excel": "^1.7.14", 
  "@vue-office/pdf": "^2.0.10",
  "vue-demi": "^0.14.6"
}
```

### 支持的文档类型
| 类型 | 预览方式 | 状态 |
|------|---------|------|
| Word (.docx, .doc) | vue-office | ✅ 目标支持 |
| Excel (.xlsx, .xls) | vue-office | ✅ 目标支持 |
| PowerPoint (.pptx, .ppt) | 不支持 | ⚠️ 需下载 |
| PDF (.pdf) | iframe | ✅ 保持现状 |

---

## 下次工作计划

1. **立即任务**：完成后端修改验证
2. **后续任务**：根据验证结果决定下一步
3. **最终目标**：确保Office文档能够通过vue-office正常预览

---

## 备注
- 所有修改都要先在开发环境验证
- 每个步骤完成后更新此日志
- 遇到问题及时记录解决方案
