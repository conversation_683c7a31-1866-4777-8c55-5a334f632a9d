#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档预览缓存服务

提供Redis缓存机制，提升文档预览性能
"""
import json
import hashlib
import os
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import redis.asyncio as aioredis

from backend.common.log import log as logger
from backend.core.conf import settings


class DocumentCache:
    """文档预览缓存服务"""
    
    def __init__(self, redis_url: Optional[str] = None):
        """
        初始化缓存服务
        
        Args:
            redis_url: Redis连接URL，如果为None则使用配置文件中的设置
        """
        self.redis_url = redis_url or f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DATABASE}"
        self.redis = None
        
        # 缓存TTL设置（秒）
        self.preview_url_ttl = 3600 * 24      # 预览URL缓存24小时
        self.conversion_result_ttl = 3600 * 24 * 7  # 转换结果缓存7天
        self.document_info_ttl = 3600 * 12    # 文档信息缓存12小时
        self.progress_ttl = 3600              # 转换进度缓存1小时
        
        # 缓存键前缀
        self.prefix = "doc_preview:"
        
    async def init_redis(self):
        """初始化Redis连接"""
        if self.redis is None:
            try:
                # 构建Redis连接参数
                redis_params = {
                    'host': settings.REDIS_HOST,
                    'port': settings.REDIS_PORT,
                    'db': settings.REDIS_DATABASE,
                    'encoding': 'utf-8',
                    'decode_responses': True
                }

                # 如果有密码则添加
                if hasattr(settings, 'REDIS_PASSWORD') and settings.REDIS_PASSWORD:
                    redis_params['password'] = settings.REDIS_PASSWORD

                self.redis = aioredis.Redis(**redis_params)

                # 测试连接
                await self.redis.ping()
                logger.info("Redis连接初始化成功")
            except Exception as e:
                logger.error(f"Redis连接失败: {e}")
                raise
                
    async def close(self):
        """关闭Redis连接"""
        if self.redis:
            await self.redis.close()
            
    def _get_cache_key(self, key_type: str, *args) -> str:
        """生成缓存键"""
        key_parts = [self.prefix, key_type] + [str(arg) for arg in args]
        return ":".join(key_parts)
        
    def _get_document_hash(self, doc_id: str, doc_modified: str = None) -> str:
        """生成文档唯一标识哈希"""
        if doc_modified:
            content = f"{doc_id}:{doc_modified}"
        else:
            content = doc_id
        return hashlib.md5(content.encode()).hexdigest()
        
    async def get_preview_url(self, 
                            doc_id: str, 
                            doc_modified: str = None) -> Optional[str]:
        """
        获取文档预览URL缓存
        
        Args:
            doc_id: 文档ID
            doc_modified: 文档修改时间
            
        Returns:
            缓存的预览URL，如果不存在则返回None
        """
        await self.init_redis()
        
        doc_hash = self._get_document_hash(doc_id, doc_modified)
        cache_key = self._get_cache_key("preview_url", doc_hash)
        
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                logger.debug(f"预览URL缓存命中: {doc_id}")
                return data.get('url')
        except Exception as e:
            logger.error(f"获取预览URL缓存失败: {e}")
            
        return None
        
    async def cache_preview_url(self, 
                              doc_id: str, 
                              preview_url: str,
                              doc_modified: str = None,
                              metadata: Dict[str, Any] = None):
        """
        缓存文档预览URL
        
        Args:
            doc_id: 文档ID
            preview_url: 预览URL
            doc_modified: 文档修改时间
            metadata: 额外的元数据
        """
        await self.init_redis()
        
        doc_hash = self._get_document_hash(doc_id, doc_modified)
        cache_key = self._get_cache_key("preview_url", doc_hash)
        
        cache_data = {
            'url': preview_url,
            'doc_id': doc_id,
            'cached_at': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        try:
            await self.redis.setex(
                cache_key, 
                self.preview_url_ttl, 
                json.dumps(cache_data)
            )
            logger.debug(f"预览URL已缓存: {doc_id}")
        except Exception as e:
            logger.error(f"缓存预览URL失败: {e}")
            
    async def get_conversion_result(self, 
                                  doc_id: str, 
                                  doc_modified: str = None) -> Optional[Dict[str, Any]]:
        """
        获取文档转换结果缓存
        
        Args:
            doc_id: 文档ID
            doc_modified: 文档修改时间
            
        Returns:
            缓存的转换结果，如果不存在则返回None
        """
        await self.init_redis()
        
        doc_hash = self._get_document_hash(doc_id, doc_modified)
        cache_key = self._get_cache_key("conversion", doc_hash)
        
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                # 检查转换后的文件是否仍然存在
                output_file = data.get('output_file')
                if output_file and os.path.exists(output_file):
                    logger.debug(f"转换结果缓存命中: {doc_id}")
                    return data
                else:
                    # 文件不存在，删除缓存
                    await self.redis.delete(cache_key)
                    logger.warning(f"转换结果文件不存在，删除缓存: {output_file}")
        except Exception as e:
            logger.error(f"获取转换结果缓存失败: {e}")
            
        return None
        
    async def cache_conversion_result(self, 
                                    doc_id: str, 
                                    result: Dict[str, Any],
                                    doc_modified: str = None):
        """
        缓存文档转换结果
        
        Args:
            doc_id: 文档ID
            result: 转换结果
            doc_modified: 文档修改时间
        """
        await self.init_redis()
        
        doc_hash = self._get_document_hash(doc_id, doc_modified)
        cache_key = self._get_cache_key("conversion", doc_hash)
        
        cache_data = {
            **result,
            'doc_id': doc_id,
            'cached_at': datetime.now().isoformat()
        }
        
        try:
            await self.redis.setex(
                cache_key, 
                self.conversion_result_ttl, 
                json.dumps(cache_data, default=str)
            )
            logger.debug(f"转换结果已缓存: {doc_id}")
        except Exception as e:
            logger.error(f"缓存转换结果失败: {e}")
            
    async def get_document_info(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """获取文档信息缓存"""
        await self.init_redis()
        
        cache_key = self._get_cache_key("doc_info", doc_id)
        
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                logger.debug(f"文档信息缓存命中: {doc_id}")
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"获取文档信息缓存失败: {e}")
            
        return None
        
    async def cache_document_info(self, doc_id: str, doc_info: Dict[str, Any]):
        """缓存文档信息"""
        await self.init_redis()
        
        cache_key = self._get_cache_key("doc_info", doc_id)
        
        cache_data = {
            **doc_info,
            'cached_at': datetime.now().isoformat()
        }
        
        try:
            await self.redis.setex(
                cache_key, 
                self.document_info_ttl, 
                json.dumps(cache_data, default=str)
            )
            logger.debug(f"文档信息已缓存: {doc_id}")
        except Exception as e:
            logger.error(f"缓存文档信息失败: {e}")
            
    async def set_conversion_progress(self, 
                                    task_id: str, 
                                    progress: float, 
                                    status: str,
                                    message: str = ""):
        """
        设置转换进度
        
        Args:
            task_id: 任务ID
            progress: 进度百分比 (0.0-1.0)
            status: 状态 (pending, processing, completed, failed)
            message: 状态消息
        """
        await self.init_redis()
        
        cache_key = self._get_cache_key("progress", task_id)
        
        progress_data = {
            'task_id': task_id,
            'progress': progress,
            'status': status,
            'message': message,
            'updated_at': datetime.now().isoformat()
        }
        
        try:
            await self.redis.setex(
                cache_key, 
                self.progress_ttl, 
                json.dumps(progress_data)
            )
        except Exception as e:
            logger.error(f"设置转换进度失败: {e}")
            
    async def get_conversion_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取转换进度"""
        await self.init_redis()
        
        cache_key = self._get_cache_key("progress", task_id)
        
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.error(f"获取转换进度失败: {e}")
            
        return None
        
    async def invalidate_document_cache(self, doc_id: str):
        """清除文档相关的所有缓存"""
        await self.init_redis()
        
        # 获取所有相关的缓存键
        pattern = self._get_cache_key("*", f"*{doc_id}*")
        
        try:
            keys = await self.redis.keys(pattern)
            if keys:
                await self.redis.delete(*keys)
                logger.info(f"已清除文档缓存: {doc_id}, 删除键数量: {len(keys)}")
        except Exception as e:
            logger.error(f"清除文档缓存失败: {e}")
            
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        await self.init_redis()
        
        try:
            info = await self.redis.info()
            
            # 获取各类型缓存的键数量
            preview_keys = await self.redis.keys(self._get_cache_key("preview_url", "*"))
            conversion_keys = await self.redis.keys(self._get_cache_key("conversion", "*"))
            doc_info_keys = await self.redis.keys(self._get_cache_key("doc_info", "*"))
            progress_keys = await self.redis.keys(self._get_cache_key("progress", "*"))
            
            return {
                'redis_info': {
                    'used_memory': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients'),
                    'total_commands_processed': info.get('total_commands_processed')
                },
                'cache_counts': {
                    'preview_urls': len(preview_keys),
                    'conversion_results': len(conversion_keys),
                    'document_info': len(doc_info_keys),
                    'progress_tracking': len(progress_keys)
                }
            }
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}
            
    async def cleanup_expired_cache(self):
        """清理过期的缓存（手动清理，Redis会自动过期）"""
        await self.init_redis()
        
        try:
            # 这里可以添加自定义的清理逻辑
            # 比如清理文件不存在的转换结果缓存
            conversion_keys = await self.redis.keys(self._get_cache_key("conversion", "*"))
            
            cleaned_count = 0
            for key in conversion_keys:
                try:
                    cached_data = await self.redis.get(key)
                    if cached_data:
                        data = json.loads(cached_data)
                        output_file = data.get('output_file')
                        if output_file and not os.path.exists(output_file):
                            await self.redis.delete(key)
                            cleaned_count += 1
                except Exception:
                    continue
                    
            logger.info(f"清理过期缓存完成，删除 {cleaned_count} 个无效缓存")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
            return 0

    async def set_document_converted(self, doc_id: str, converted: bool) -> bool:
        """设置文档转换状态"""
        await self.init_redis()
        try:
            key = f"doc_converted:{doc_id}"
            await self.redis.set(key, json.dumps(converted), ex=86400)  # 24小时过期
            return True
        except Exception as e:
            logger.error(f"设置文档转换状态失败: {str(e)}")
            return False

    async def is_document_converted(self, doc_id: str) -> bool:
        """检查文档是否已转换"""
        await self.init_redis()
        try:
            key = f"doc_converted:{doc_id}"
            result = await self.redis.get(key)
            if result:
                return json.loads(result)
            return False
        except Exception as e:
            logger.error(f"检查文档转换状态失败: {str(e)}")
            return False


# 创建全局缓存实例
document_cache = DocumentCache()
