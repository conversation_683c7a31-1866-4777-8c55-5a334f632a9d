#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档转换服务

提供LibreOffice文档转换功能，支持Office文档转PDF
"""
import asyncio
import os
import subprocess
import tempfile
import uuid
from pathlib import Path
from typing import Optional, Dict, Any
import hashlib
import shutil

from backend.common.log import log as logger
from backend.common.exception.errors import BaseExceptionMixin


class ConversionError(BaseExceptionMixin):
    """文档转换异常"""

    def __init__(self, msg: str = "文档转换失败"):
        self.code = 500
        super().__init__(msg=msg)


class DocumentConverter:
    """文档转换服务"""
    
    def __init__(self, 
                 libreoffice_path: str = "/usr/bin/libreoffice",
                 max_concurrent: int = 3,
                 timeout: int = 300,
                 temp_dir: Optional[str] = None):
        """
        初始化文档转换器
        
        Args:
            libreoffice_path: LibreOffice可执行文件路径
            max_concurrent: 最大并发转换数
            timeout: 转换超时时间（秒）
            temp_dir: 临时文件目录
        """
        self.libreoffice_path = libreoffice_path
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.timeout = timeout
        self.temp_dir = temp_dir or tempfile.gettempdir()
        
        # 支持的文档格式
        self.supported_formats = {
            '.doc', '.docx',      # Word文档
            '.xls', '.xlsx',      # Excel表格
            '.ppt', '.pptx',      # PowerPoint演示文稿
            '.odt', '.ods', '.odp', # OpenDocument格式
            '.rtf',               # Rich Text Format
        }
        
    def is_supported_format(self, filename: str) -> bool:
        """检查是否支持的文档格式"""
        ext = Path(filename).suffix.lower()
        return ext in self.supported_formats
        
    def _get_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
        
    async def convert_to_pdf(self, 
                           input_file: str, 
                           output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        将Office文档转换为PDF
        
        Args:
            input_file: 输入文件路径
            output_dir: 输出目录，如果为None则使用临时目录
            
        Returns:
            转换结果字典，包含输出文件路径、文件大小等信息
            
        Raises:
            ConversionError: 转换失败时抛出
        """
        if not os.path.exists(input_file):
            raise ConversionError(f"输入文件不存在: {input_file}")
            
        if not self.is_supported_format(input_file):
            raise ConversionError(f"不支持的文件格式: {Path(input_file).suffix}")
            
        # 使用信号量控制并发
        async with self.semaphore:
            return await self._do_convert(input_file, output_dir)
            
    async def _do_convert(self, input_file: str, output_dir: Optional[str]) -> Dict[str, Any]:
        """执行实际的转换操作"""
        start_time = asyncio.get_event_loop().time()
        
        # 准备输出目录
        if output_dir is None:
            output_dir = os.path.join(self.temp_dir, f"conversion_{uuid.uuid4().hex}")
            os.makedirs(output_dir, exist_ok=True)
            temp_output = True
        else:
            os.makedirs(output_dir, exist_ok=True)
            temp_output = False
            
        try:
            # 构建转换命令
            cmd = [
                self.libreoffice_path,
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', output_dir,
                input_file
            ]
            
            logger.info(f"开始转换文档: {input_file} -> {output_dir}")
            
            # 执行转换
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=self.timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise ConversionError(f"转换超时 ({self.timeout}秒)")
                
            # 检查转换结果
            if process.returncode != 0:
                error_msg = stderr.decode('utf-8', errors='ignore')
                logger.error(f"LibreOffice转换失败: {error_msg}")
                raise ConversionError(f"转换失败: {error_msg}")
                
            # 查找输出文件
            input_name = Path(input_file).stem
            output_file = os.path.join(output_dir, f"{input_name}.pdf")
            
            if not os.path.exists(output_file):
                raise ConversionError(f"转换完成但找不到输出文件: {output_file}")
                
            # 计算转换时间和文件信息
            conversion_time = asyncio.get_event_loop().time() - start_time
            file_size = os.path.getsize(output_file)
            file_hash = self._get_file_hash(output_file)
            
            logger.info(f"文档转换成功: {input_file} -> {output_file} "
                       f"(耗时: {conversion_time:.2f}秒, 大小: {file_size} bytes)")
            
            return {
                'success': True,
                'output_file': output_file,
                'output_dir': output_dir,
                'file_size': file_size,
                'file_hash': file_hash,
                'conversion_time': conversion_time,
                'temp_output': temp_output,
                'original_file': input_file
            }
            
        except Exception as e:
            # 清理临时目录
            if temp_output and os.path.exists(output_dir):
                shutil.rmtree(output_dir, ignore_errors=True)
            raise ConversionError(f"转换过程中发生错误: {str(e)}")
            
    async def batch_convert(self, 
                          input_files: list, 
                          output_dir: str) -> Dict[str, Any]:
        """
        批量转换文档
        
        Args:
            input_files: 输入文件列表
            output_dir: 输出目录
            
        Returns:
            批量转换结果
        """
        os.makedirs(output_dir, exist_ok=True)
        
        tasks = []
        for input_file in input_files:
            task = self.convert_to_pdf(input_file, output_dir)
            tasks.append(task)
            
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = 0
        failed_count = 0
        failed_files = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_count += 1
                failed_files.append({
                    'file': input_files[i],
                    'error': str(result)
                })
                logger.error(f"批量转换失败: {input_files[i]} - {result}")
            else:
                success_count += 1
                
        return {
            'total': len(input_files),
            'success': success_count,
            'failed': failed_count,
            'failed_files': failed_files,
            'results': results
        }
        
    def cleanup_temp_files(self, result: Dict[str, Any]):
        """清理临时文件"""
        if result.get('temp_output') and result.get('output_dir'):
            output_dir = result['output_dir']
            if os.path.exists(output_dir):
                shutil.rmtree(output_dir, ignore_errors=True)
                logger.info(f"清理临时目录: {output_dir}")
                
    async def get_conversion_info(self, input_file: str) -> Dict[str, Any]:
        """
        获取文档转换信息（不执行实际转换）
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            文档信息字典
        """
        if not os.path.exists(input_file):
            raise ConversionError(f"文件不存在: {input_file}")
            
        file_path = Path(input_file)
        file_size = os.path.getsize(input_file)
        file_hash = self._get_file_hash(input_file)
        
        return {
            'filename': file_path.name,
            'extension': file_path.suffix.lower(),
            'size': file_size,
            'hash': file_hash,
            'supported': self.is_supported_format(input_file),
            'estimated_time': self._estimate_conversion_time(file_size)
        }
        
    def _estimate_conversion_time(self, file_size: int) -> float:
        """估算转换时间（基于文件大小）"""
        # 简单的线性估算：1MB约需要2秒
        mb_size = file_size / (1024 * 1024)
        return max(5.0, mb_size * 2.0)  # 最少5秒


# 创建全局转换器实例
document_converter = DocumentConverter()
