2025-08-14 08:00:41.561 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002124115E8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x00000212436B8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x00000212436BBA60>
    └ <uvicorn.server.Server object at 0x000002124A3B49E0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000212436BBB00>
           │       │   └ <uvicorn.server.Server object at 0x000002124A3B49E0>
           │       └ <function run at 0x00000212418DF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002124B5AE7A0>
           │      └ <function Runner.run at 0x0000021242F9B2E0>
           └ <asyncio.runners.Runner object at 0x000002124A55BD40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000021242F98EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002124A55BD40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000021243068D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000021242F9AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000212418D4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1760, family=2, type=1, proto=6, laddr=('*************', 62423), raddr=('*************', 5981)>
    └ <_ProactorSocketTransport closing fd=1760>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-14 08:25:10.792 | ERROR    | 27e54b5612134168b535707b5f373b25 | Java token认证失败: 401: Token 格式错误
2025-08-14 08:38:15.376 | ERROR    | 1a7f186bb41746948f70a99f6f8b0058 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:15.377 | ERROR    | 1a7f186bb41746948f70a99f6f8b0058 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:16.934 | ERROR    | 16809cb9e498412982edf514785613d3 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:16.934 | ERROR    | 16809cb9e498412982edf514785613d3 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:17.837 | ERROR    | 9b30aa5a8a9145d388b73a14d069380e | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:17.837 | ERROR    | 9b30aa5a8a9145d388b73a14d069380e | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:18.325 | ERROR    | dbe72f42833041b0ba03035a018dba71 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:18.325 | ERROR    | dbe72f42833041b0ba03035a018dba71 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:18.663 | ERROR    | 8cb7ecbe16ce4ce1a68315bf7b3f98cc | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:18.663 | ERROR    | 8cb7ecbe16ce4ce1a68315bf7b3f98cc | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:18.936 | ERROR    | d29c879adaf3435e9b69d24f69229b68 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:18.937 | ERROR    | d29c879adaf3435e9b69d24f69229b68 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:19.215 | ERROR    | 8b09cbc968cb46e288d0be7a01d961f5 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:19.215 | ERROR    | 8b09cbc968cb46e288d0be7a01d961f5 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:19.521 | ERROR    | e6c8f4e5a6e44fa1a6bc71c8166d7167 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:19.521 | ERROR    | e6c8f4e5a6e44fa1a6bc71c8166d7167 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:19.841 | ERROR    | 78472d74597c4cc79b4acc5aa79a2ec9 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:19.841 | ERROR    | 78472d74597c4cc79b4acc5aa79a2ec9 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:20.101 | ERROR    | e27fae9dd8414677898e736977ee37db | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:20.101 | ERROR    | e27fae9dd8414677898e736977ee37db | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:20.371 | ERROR    | a15cc5248de041b1ad42ae91e45ffc9b | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:20.371 | ERROR    | a15cc5248de041b1ad42ae91e45ffc9b | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:20.838 | ERROR    | e8637b8e47894e148544b8c238a9bc8c | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:20.838 | ERROR    | e8637b8e47894e148544b8c238a9bc8c | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:21.110 | ERROR    | d0796777e0c34217a6c1e7a3de419585 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:21.111 | ERROR    | d0796777e0c34217a6c1e7a3de419585 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:21.345 | ERROR    | fac7783ff9a441239636db0618aad78e | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:21.345 | ERROR    | fac7783ff9a441239636db0618aad78e | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:44.856 | ERROR    | fb5bb4d261fc4d5184e1687171682353 | 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:38:44.856 | ERROR    | fb5bb4d261fc4d5184e1687171682353 | 获取文档预览失败: 获取文档预览失败: name 'document_cache' is not defined
2025-08-14 08:39:42.788 | ERROR    | 31f003c3863b4ef18e08f60f4cc99258 | 文档转换失败: DocumentService.get_document_preview() takes 3 positional arguments but 4 were given
2025-08-14 08:39:42.788 | ERROR    | 31f003c3863b4ef18e08f60f4cc99258 | 文档转换失败: 文档转换失败: DocumentService.get_document_preview() takes 3 positional arguments but 4 were given
2025-08-14 10:08:01.868 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000029E4AACA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000029E4CFC8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000029E4CFCBA60>
    └ <uvicorn.server.Server object at 0x0000029E53D3E240>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000029E4CFCBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000029E53D3E240>
           │       └ <function run at 0x0000029E4B1EF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000029E54E9E960>
           │      └ <function Runner.run at 0x0000029E4CCAB2E0>
           └ <asyncio.runners.Runner object at 0x0000029E539236E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029E4CCA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000029E539236E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000029E4CD78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029E4CCAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000029E4B1E4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1284, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 54840)>
    └ <_ProactorSocketTransport closing fd=1284>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-14 10:22:57.627 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000029E4AACA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000029E4CFC8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000029E4CFCBA60>
    └ <uvicorn.server.Server object at 0x0000029E53D3E240>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000029E4CFCBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000029E53D3E240>
           │       └ <function run at 0x0000029E4B1EF060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000029E54E9E960>
           │      └ <function Runner.run at 0x0000029E4CCAB2E0>
           └ <asyncio.runners.Runner object at 0x0000029E539236E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029E4CCA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000029E539236E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000029E4CD78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029E4CCAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000029E4B1E4860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1980, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 56593)>
    └ <_ProactorSocketTransport closing fd=1980>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
