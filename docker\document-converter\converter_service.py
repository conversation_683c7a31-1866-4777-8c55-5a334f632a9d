#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LibreOffice文档转换微服务

提供HTTP API接口进行文档转换
"""
import os
import tempfile
import uuid
from pathlib import Path
from typing import Optional

from fastapi import FastAPI, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
import aiofiles
import asyncio
import subprocess
import shutil

app = FastAPI(
    title="Document Converter Service",
    description="LibreOffice文档转换服务",
    version="1.0.0"
)

# 配置
TEMP_DIR = "/app/temp"
OUTPUT_DIR = "/app/output"
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
LIBREOFFICE_PATH = "/usr/bin/libreoffice"

# 支持的文件格式
SUPPORTED_FORMATS = {
    '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.odt', '.ods', '.odp', '.rtf'
}


class ConversionError(Exception):
    """转换异常"""
    pass


async def convert_document(input_file: str, output_dir: str) -> str:
    """
    转换文档为PDF
    
    Args:
        input_file: 输入文件路径
        output_dir: 输出目录
        
    Returns:
        转换后的PDF文件路径
    """
    if not os.path.exists(input_file):
        raise ConversionError(f"输入文件不存在: {input_file}")
        
    # 检查文件格式
    file_ext = Path(input_file).suffix.lower()
    if file_ext not in SUPPORTED_FORMATS:
        raise ConversionError(f"不支持的文件格式: {file_ext}")
        
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建转换命令
    cmd = [
        LIBREOFFICE_PATH,
        '--headless',
        '--convert-to', 'pdf',
        '--outdir', output_dir,
        input_file
    ]
    
    try:
        # 执行转换
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await asyncio.wait_for(
            process.communicate(), 
            timeout=300  # 5分钟超时
        )
        
        if process.returncode != 0:
            error_msg = stderr.decode('utf-8', errors='ignore')
            raise ConversionError(f"转换失败: {error_msg}")
            
        # 查找输出文件
        input_name = Path(input_file).stem
        output_file = os.path.join(output_dir, f"{input_name}.pdf")
        
        if not os.path.exists(output_file):
            raise ConversionError(f"转换完成但找不到输出文件: {output_file}")
            
        return output_file
        
    except asyncio.TimeoutError:
        raise ConversionError("转换超时")
    except Exception as e:
        raise ConversionError(f"转换过程中发生错误: {str(e)}")


async def cleanup_old_files():
    """清理旧文件"""
    try:
        # 清理超过1小时的临时文件
        for directory in [TEMP_DIR, OUTPUT_DIR]:
            if os.path.exists(directory):
                for file_path in Path(directory).glob("*"):
                    if file_path.is_file():
                        # 检查文件修改时间
                        file_age = asyncio.get_event_loop().time() - file_path.stat().st_mtime
                        if file_age > 3600:  # 1小时
                            file_path.unlink()
    except Exception as e:
        print(f"清理文件失败: {e}")


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    # 创建必要的目录
    os.makedirs(TEMP_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 启动定期清理任务
    asyncio.create_task(periodic_cleanup())


async def periodic_cleanup():
    """定期清理任务"""
    while True:
        await asyncio.sleep(3600)  # 每小时执行一次
        await cleanup_old_files()


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "document-converter",
        "libreoffice_available": os.path.exists(LIBREOFFICE_PATH)
    }


@app.post("/convert")
async def convert_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    output_format: str = "pdf"
):
    """
    转换文档
    
    Args:
        file: 上传的文件
        output_format: 输出格式（目前只支持pdf）
        
    Returns:
        转换结果
    """
    if output_format != "pdf":
        raise HTTPException(status_code=400, detail="目前只支持转换为PDF格式")
        
    # 检查文件大小
    if file.size and file.size > MAX_FILE_SIZE:
        raise HTTPException(status_code=413, detail=f"文件大小超过限制 ({MAX_FILE_SIZE} bytes)")
        
    # 检查文件格式
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in SUPPORTED_FORMATS:
        raise HTTPException(status_code=400, detail=f"不支持的文件格式: {file_ext}")
        
    # 生成唯一的任务ID
    task_id = str(uuid.uuid4())
    
    # 创建临时文件
    temp_input = os.path.join(TEMP_DIR, f"{task_id}_input{file_ext}")
    temp_output_dir = os.path.join(OUTPUT_DIR, task_id)
    
    try:
        # 保存上传的文件
        async with aiofiles.open(temp_input, 'wb') as f:
            content = await file.read()
            await f.write(content)
            
        # 执行转换
        output_file = await convert_document(temp_input, temp_output_dir)
        
        # 计算文件信息
        file_size = os.path.getsize(output_file)
        
        # 添加清理任务
        background_tasks.add_task(cleanup_task_files, temp_input, temp_output_dir)
        
        return {
            "success": True,
            "task_id": task_id,
            "output_file": output_file,
            "file_size": file_size,
            "download_url": f"/download/{task_id}"
        }
        
    except ConversionError as e:
        # 清理临时文件
        if os.path.exists(temp_input):
            os.unlink(temp_input)
        if os.path.exists(temp_output_dir):
            shutil.rmtree(temp_output_dir, ignore_errors=True)
            
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        # 清理临时文件
        if os.path.exists(temp_input):
            os.unlink(temp_input)
        if os.path.exists(temp_output_dir):
            shutil.rmtree(temp_output_dir, ignore_errors=True)
            
        raise HTTPException(status_code=500, detail=f"转换失败: {str(e)}")


@app.get("/download/{task_id}")
async def download_file(task_id: str):
    """
    下载转换后的文件
    
    Args:
        task_id: 任务ID
        
    Returns:
        文件下载响应
    """
    output_dir = os.path.join(OUTPUT_DIR, task_id)
    
    if not os.path.exists(output_dir):
        raise HTTPException(status_code=404, detail="文件不存在或已过期")
        
    # 查找PDF文件
    pdf_files = list(Path(output_dir).glob("*.pdf"))
    if not pdf_files:
        raise HTTPException(status_code=404, detail="转换后的文件不存在")
        
    pdf_file = pdf_files[0]
    
    return FileResponse(
        path=str(pdf_file),
        filename=pdf_file.name,
        media_type="application/pdf"
    )


@app.get("/status/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务状态信息
    """
    output_dir = os.path.join(OUTPUT_DIR, task_id)
    
    if os.path.exists(output_dir):
        pdf_files = list(Path(output_dir).glob("*.pdf"))
        if pdf_files:
            return {
                "task_id": task_id,
                "status": "completed",
                "file_size": pdf_files[0].stat().st_size
            }
        else:
            return {
                "task_id": task_id,
                "status": "processing"
            }
    else:
        return {
            "task_id": task_id,
            "status": "not_found"
        }


async def cleanup_task_files(input_file: str, output_dir: str):
    """清理任务文件"""
    await asyncio.sleep(3600)  # 1小时后清理
    
    try:
        if os.path.exists(input_file):
            os.unlink(input_file)
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir, ignore_errors=True)
    except Exception as e:
        print(f"清理任务文件失败: {e}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
