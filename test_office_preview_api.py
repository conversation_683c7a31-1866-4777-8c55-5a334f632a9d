#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Office文档预览API的脚本
用于验证后端是否正确返回office类型而不是converted.pdf URL
"""

import asyncio
import aiohttp
import json
import sys
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"  # 根据实际后端地址调整
TEST_CASES = [
    {
        "name": "Word文档测试",
        "kb_id": "test-kb-id",
        "doc_id": "test-word-doc-id",
        "expected_content_type": "office"
    },
    {
        "name": "Excel文档测试", 
        "kb_id": "test-kb-id",
        "doc_id": "test-excel-doc-id",
        "expected_content_type": "office"
    }
]

class OfficePreviewTester:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_preview_api(self, kb_id: str, doc_id: str) -> Dict[str, Any]:
        """测试文档预览API"""
        url = f"{self.base_url}/api/iot/v1/documents/{kb_id}/{doc_id}/preview"
        
        # 这里需要添加实际的认证token
        headers = {
            "Authorization": "Bearer YOUR_TOKEN_HERE",  # 替换为实际token
            "Content-Type": "application/json"
        }
        
        try:
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        "success": True,
                        "status_code": response.status,
                        "data": result
                    }
                else:
                    text = await response.text()
                    return {
                        "success": False,
                        "status_code": response.status,
                        "error": text
                    }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def analyze_response(self, response: Dict[str, Any], expected_content_type: str) -> Dict[str, Any]:
        """分析API响应"""
        analysis = {
            "test_passed": False,
            "issues": [],
            "details": {}
        }
        
        if not response.get("success"):
            analysis["issues"].append(f"API调用失败: {response.get('error', 'Unknown error')}")
            return analysis
        
        data = response.get("data", {})
        if "data" in data:
            preview_data = data["data"]
        else:
            preview_data = data
            
        analysis["details"] = preview_data
        
        # 检查content_type
        content_type = preview_data.get("content_type")
        if content_type != expected_content_type:
            analysis["issues"].append(f"content_type错误: 期望'{expected_content_type}', 实际'{content_type}'")
        
        # 检查URL
        url = preview_data.get("url", "")
        if "converted.pdf" in url:
            analysis["issues"].append(f"仍然返回converted.pdf URL: {url}")
        elif "/download" not in url:
            analysis["issues"].append(f"URL格式异常: {url}")
        
        # 检查requires_conversion
        requires_conversion = preview_data.get("requires_conversion")
        if requires_conversion is True:
            analysis["issues"].append("requires_conversion应该为False")
        
        # 如果没有问题，测试通过
        if not analysis["issues"]:
            analysis["test_passed"] = True
            
        return analysis
    
    async def run_tests(self):
        """运行所有测试"""
        print("🧪 开始测试Office文档预览API...")
        print("=" * 60)
        
        all_passed = True
        
        for i, test_case in enumerate(TEST_CASES, 1):
            print(f"\n📋 测试 {i}: {test_case['name']}")
            print("-" * 40)
            
            # 调用API
            response = await self.test_preview_api(
                test_case["kb_id"], 
                test_case["doc_id"]
            )
            
            # 分析结果
            analysis = self.analyze_response(
                response, 
                test_case["expected_content_type"]
            )
            
            # 输出结果
            if analysis["test_passed"]:
                print("✅ 测试通过")
                print(f"   content_type: {analysis['details'].get('content_type')}")
                print(f"   url: {analysis['details'].get('url')}")
                print(f"   message: {analysis['details'].get('message')}")
            else:
                print("❌ 测试失败")
                for issue in analysis["issues"]:
                    print(f"   - {issue}")
                all_passed = False
                
                # 输出详细信息用于调试
                print("\n🔍 详细响应数据:")
                print(json.dumps(analysis["details"], indent=2, ensure_ascii=False))
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 所有测试通过！Office文档预览API工作正常。")
        else:
            print("⚠️  部分测试失败，需要进一步检查。")
            
        return all_passed

async def main():
    """主函数"""
    print("Office文档预览API测试工具")
    print("用于验证后端是否正确返回office类型而不是converted.pdf URL")
    print()
    
    # 检查是否需要更新配置
    if "YOUR_TOKEN_HERE" in open(__file__).read():
        print("⚠️  请先在脚本中配置正确的:")
        print("   1. BASE_URL (后端服务地址)")
        print("   2. Authorization token")
        print("   3. 实际的kb_id和doc_id")
        print()
        print("配置完成后重新运行此脚本。")
        return
    
    async with OfficePreviewTester(BASE_URL) as tester:
        success = await tester.run_tests()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
